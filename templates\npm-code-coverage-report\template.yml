spec:
  inputs:
    nodejs-image-name:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/nodejs:22-latest
    code-quality-stage:
      default: .post
      description: "Stage for running coverage reports"
    log-level:
      default: "DEBUG"
    test-job-name:
      default: "npm-test"
      description: "Name of the test job that produces coverage data"
    artifacts-expiry:
      default: "1 week"
      description: "How long to keep the coverage artifacts"
    coverage-threshold:
      default: 80
      description: "Minimum coverage percentage required"
    enable-pages:
      default: "false"
      description: "Enable GitLab Pages deployment for coverage reports"
    pages-stage:
      default: ".post"
      description: "Stage for GitLab Pages deployment"

---

check-coverage:
  stage: $[[ inputs.code-quality-stage ]]
  image: $[[ inputs.nodejs-image-name ]]
  needs: 
    - job: "$[[ inputs.test-job-name ]]"
      optional: true
  rules:
    - if: '$CI_COMMIT_TAG'
      when: never
    - when: always
  variables:
    LOG_LEVEL: $[[ inputs.log-level ]]
    COVERAGE_THRESHOLD: $[[ inputs.coverage-threshold ]]
  dependencies: ["$[[ inputs.test-job-name ]]"]
  before_script:
    - npm ci --ignore-engines
  script:
    - echo "Running coverage threshold check..."
    - |
      cat > check-coverage.cjs << 'EOF'
      const fs = require('fs');
      const path = require('path');

      const COVERAGE_THRESHOLD = parseInt(process.env.COVERAGE_THRESHOLD) || 80;

      function formatUrl(baseUrl, path) {
        return `${baseUrl}${path}`.replace(/([^:]\/)\/+/g, '$1');
      }

      function checkCoverage() {
        const coveragePath = path.join(process.cwd(), 'coverage', 'coverage-summary.json');
        
        if (!fs.existsSync(coveragePath)) {
          console.error('❌ Coverage summary not found!');
          console.error('Make sure tests ran successfully and coverage was generated.');
          process.exit(1);
        }

        const coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
        const { lines, functions, branches, statements } = coverage.total;

        // Metrics that must meet threshold (will cause pipeline failure)
        const criticalMetrics = [
          { name: 'Lines', value: lines.pct },
          { name: 'Branches', value: branches.pct }
        ];

        // Metrics that are only displayed (informational only)
        const informationalMetrics = [
          { name: 'Functions', value: functions.pct },
          { name: 'Statements', value: statements.pct }
        ];

        console.log('\n' + '='.repeat(50));
        console.log('📊 COVERAGE REPORT');
        console.log('='.repeat(50));

        let failed = false;
        let failedMetrics = [];

        // Check critical metrics against threshold
        console.log('🔍 THRESHOLD-CHECKED METRICS:');
        criticalMetrics.forEach(metric => {
          const passed = metric.value >= COVERAGE_THRESHOLD;
          const status = passed ? '✅' : '❌';
          const gap = passed ? '' : ` (${(COVERAGE_THRESHOLD - metric.value).toFixed(1)}% short)`;

          console.log(`${status} ${metric.name.padEnd(12)}: ${metric.value.toFixed(1)}%${gap}`);

          if (!passed) {
            failed = true;
            failedMetrics.push(metric.name.toLowerCase());
          }
        });

        // Display informational metrics (no threshold check)
        console.log('\n📋 INFORMATIONAL METRICS:');
        informationalMetrics.forEach(metric => {
          const status = '📊'; // Always informational icon
          console.log(`${status} ${metric.name.padEnd(12)}: ${metric.value.toFixed(1)}%`);
        });

        console.log('='.repeat(50));

        if (failed) {
          console.log('❌ COVERAGE CHECK FAILED');
          console.log(`Required threshold: ${COVERAGE_THRESHOLD}%`);
          console.log(`Failed metrics: ${failedMetrics.join(', ')}`);
          console.log('');
          
          const jobUrl = process.env.CI_JOB_URL;
          const pagesUrl = process.env.CI_PAGES_URL;
          
          if (jobUrl) {
            console.log('📋 DOWNLOAD REPORTS:');
            console.log(`🔗 Coverage Report: ${formatUrl(jobUrl, '/artifacts/browse/coverage/index.html')}`);
            console.log(`🔗 Test Results: ${formatUrl(jobUrl, '/artifacts/browse/test-results/index.html')}`);
            console.log(`🔗 All Artifacts: ${formatUrl(jobUrl, '/artifacts/browse')}`);
          }
          
          if (pagesUrl) {
            console.log(`🌐 GitLab Pages: ${pagesUrl}`);
          }
          
          console.log('');
          console.log('💡 TIP: Increase test coverage to meet the threshold');
          console.log('='.repeat(50));
          
          process.exit(1);
        } else {
          console.log('✅ ALL COVERAGE THRESHOLDS PASSED!');
          if (process.env.CI_JOB_URL) {
            console.log(`📋 Not working yet - View detailed report: ${formatUrl(process.env.CI_JOB_URL, '/artifacts/browse/coverage/index.html')}`);
          }
          console.log('='.repeat(50));
        }
      }
      checkCoverage();
      EOF
    - node check-coverage.cjs    
  allow_failure: false

coverage-report:
  stage: $[[ inputs.code-quality-stage ]]
  image: $[[ inputs.nodejs-image-name ]]
  needs: 
    - job: "$[[ inputs.test-job-name ]]"
      optional: true
    - job: check-coverage
      optional: true
  rules:
    - if: '$CI_COMMIT_TAG'
      when: never
    - when: always
  variables:
    LOG_LEVEL: $[[ inputs.log-level ]]
  dependencies: ["$[[ inputs.test-job-name ]]", "check-coverage"]
  before_script:
    - npm ci --ignore-engines
  script:
    - set -xe
    - echo "Processing existing coverage data from test job..."
    - ls -la coverage/ || echo "No coverage data found"    
    - |
      if [ -f coverage/cobertura-coverage.xml ]; then
        echo "Found existing cobertura-coverage.xml, using it directly..."
      elif [ -f coverage/cobertura.xml ]; then
        echo "Found cobertura.xml, copying to cobertura-coverage.xml..."
        cp coverage/cobertura.xml coverage/cobertura-coverage.xml
      elif [ -f coverage/coverage-final.json ]; then
        echo "Found coverage-final.json, converting to cobertura-coverage.xml..."
        npx nyc report --reporter=cobertura --temp-dir=./coverage
      
        # Check if conversion was successful
        if [ -f cobertura-coverage.xml ]; then
          mv cobertura-coverage.xml coverage/cobertura-coverage.xml
          echo "Successfully converted coverage-final.json to cobertura-coverage.xml"
        else
          echo "Warning: Failed to generate cobertura-coverage.xml from coverage-final.json"
        fi
      else
        echo "No coverage data found in expected formats"
      fi
    # Create .gitignore for coverage report directory to exclude generated files from analysis
    - echo "# Exclude generated JS files in coverage from linting" > coverage/.gitignore
    - echo "*.js" >> coverage/.gitignore
    # Extract coverage percentage for GitLab UI
    - |
      if [ -f coverage/cobertura-coverage.xml ]; then
        echo "Processing coverage from cobertura-coverage.xml"
        COVERAGE=$(grep -o 'line-rate="[0-9.]*"' coverage/cobertura-coverage.xml | head -1 | grep -o '[0-9.]*' | awk '{if($1=="") print "0.00"; else printf "%.2f", $1*100}')
        # Ensure COVERAGE has a valid format for regex matching
        if [[ "$COVERAGE" =~ ^[0-9]+\.[0-9]{2}$ ]]; then
          echo "Coverage is ${COVERAGE}%"
        else
          echo "Coverage is 0.00%"
        fi
      else
        echo "No coverage data found - Coverage is 0.00%"
      fi
  coverage: '/Coverage is (\d+\.\d+)%/'
  artifacts:
    paths:
      - coverage/cobertura-coverage.xml
    expire_in: $[[ inputs.artifacts-expiry ]]
  allow_failure: true

expose-coverage-report:
  stage: $[[ inputs.code-quality-stage ]]
  image: $[[ inputs.nodejs-image-name ]]
  needs: 
    - job: coverage-report
      optional: true
  rules:
    - if: '$CI_COMMIT_TAG'
      when: never
    - when: always
  script:
    - npm ci --ignore-engines
    - cp coverage/cobertura-coverage.xml cobertura-coverage.xml
  needs: ["coverage-report"]
  artifacts:
    paths:
      - cobertura-coverage.xml
    reports:
      coverage_report:
        coverage_format: cobertura
        path: cobertura-coverage.xml
  allow_failure: true

TO-DO-pages:
  stage: $[[ inputs.pages-stage ]]
  image: $[[ inputs.nodejs-image-name ]]
  dependencies:
    - "coverage-report"
    - "$[[ inputs.test-job-name ]]"
  needs:
    - job: coverage-report
      optional: true
    - job: "$[[ inputs.test-job-name ]]"
      optional: true
  variables:
    ENABLE_PAGES: "$[[ inputs.enable-pages ]]"
  rules:
    - if: '$CI_COMMIT_TAG'
      when: never
    - if: '$ENABLE_PAGES == "true"'
      when: always
    - when: never
  script:
    - echo "Deploying coverage report to GitLab Pages..."
    - mkdir -p public
    - cp -r coverage/* public/
    - "echo \"Coverage report deployed at: $CI_PAGES_URL\""
  artifacts:
    paths:
      - public
    expire_in: $[[ inputs.artifacts-expiry ]]
  allow_failure: true

